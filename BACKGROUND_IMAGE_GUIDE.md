# Background Image Feature - Complete Implementation Guide

## Current Implementation Overview

The Dakoii Provincial Government Theme uses a **custom-built background image system** that is completely independent of WordPress's native background functionality. This implementation was designed to be simple, reliable, and conflict-free.

### Architecture Summary
- **Custom Customizer Section**: Independent "Background Image" section
- **Simple Image Upload**: Single image upload control using `WP_Customize_Image_Control`
- **Dynamic CSS Output**: PHP function generates CSS in the `<head>` section
- **Fallback System**: Cultural gradient displays when no image is uploaded
- **No WordPress Background Integration**: Avoids conflicts with WordPress's built-in background system

## How to Use the Background Image Feature

### Step-by-Step Upload Process
1. **Access WordPress Customizer**
   - Navigate to `Appearance > Customize` in your WordPress admin
   - Look for the "Background Image" section in the customizer panel

2. **Upload Your Background Image**
   - Click on the "Background Image" section to expand it
   - Click the "Upload Background Image" button
   - Choose from your media library or upload a new image
   - Select your desired image and click "Choose Image"

3. **Preview and Publish**
   - The customizer will show a live preview of your background image
   - If satisfied, click "Publish" to save your changes
   - The image will immediately appear as your site's background

### Image Requirements and Recommendations
- **Recommended Resolution**: 1920x1080 pixels or higher for full-screen coverage
- **File Format**: JPG (for photos), PNG (for graphics with transparency)
- **File Size**: Keep under 500KB for optimal loading performance
- **Aspect Ratio**: 16:9 works best for most screen sizes

## Technical Implementation Details

### Current System Architecture

#### 1. Customizer Integration (`functions.php` lines 448-463)
```php
// Custom section creation
$wp_customize->add_section('bg_image', array(
    'title' => 'Background Image',
    'priority' => 80,
));

// Setting registration
$wp_customize->add_setting('bg_image_url', array(
    'default' => '',
    'sanitize_callback' => 'esc_url_raw',
    'transport' => 'refresh',
));

// Image upload control
$wp_customize->add_control(new WP_Customize_Image_Control($wp_customize, 'bg_image_url', array(
    'label' => 'Upload Background Image',
    'section' => 'bg_image',
    'settings' => 'bg_image_url',
)));
```

#### 2. CSS Output System (`functions.php` lines 515-526)
```php
function nols_espa_background() {
    $bg_image = get_theme_mod('bg_image_url');
    $colors = nols_espa_get_current_colors();

    echo '<style>';
    if ($bg_image) {
        echo 'body { background-image: url(' . esc_url($bg_image) . '); background-size: cover; background-position: center; }';
    } else {
        echo 'body { background: linear-gradient(135deg, ' . esc_attr($colors['png-green']) . ' 0%, ' . esc_attr($colors['dark-green']) . ' 100%); }';
    }
    echo '</style>';
}
add_action('wp_head', 'nols_espa_background');
```

#### 3. CSS Base Styles (`style.css` lines 43-50)
```css
body {
    font-family: 'Quicksand', sans-serif;
    font-weight: 400;
    line-height: 1.6;
    color: var(--medium-gray);
    min-height: 100vh;
    /* Background will be handled by custom background function */
}
```

### How the System Works

#### Background Display Logic
1. **Image Uploaded**:
   - System retrieves image URL from `get_theme_mod('bg_image_url')`
   - Outputs CSS: `background-image: url(IMAGE_URL); background-size: cover; background-position: center;`
   - Image covers entire viewport with proper scaling

2. **No Image Uploaded**:
   - System falls back to cultural gradient
   - Uses theme colors from `nols_espa_get_current_colors()`
   - Outputs CSS: `background: linear-gradient(135deg, PNG_GREEN 0%, DARK_GREEN 100%);`

#### Data Storage
- **Setting Name**: `bg_image_url`
- **Storage Method**: WordPress theme modifications (`theme_mods`)
- **Database Location**: `wp_options` table under `theme_mods_dakoii-provincial-government-theme`
- **Data Format**: Full URL to uploaded image file

#### CSS Injection Process
1. **Hook**: `wp_head` action (executes in HTML `<head>` section)
2. **Priority**: Default priority (10)
3. **Output**: Inline `<style>` tag with background CSS
4. **Execution**: Runs on every page load, after main stylesheet

## System Advantages

### Why This Custom Implementation?
1. **Simplicity**: No complex WordPress background integration
2. **Reliability**: Avoids conflicts with WordPress's built-in background system
3. **Performance**: Minimal code footprint (only 16 lines total)
4. **Compatibility**: Works with all WordPress versions and themes
5. **Maintainability**: Easy to understand and modify

### Comparison with WordPress Native Background
| Feature | Custom Implementation | WordPress Native |
|---------|----------------------|------------------|
| **Complexity** | Simple (16 lines) | Complex (hundreds of lines) |
| **Conflicts** | None | Potential theme conflicts |
| **JavaScript Errors** | None | Can cause `_wpCustomizeBackground` errors |
| **Customization** | Easy to modify | Requires WordPress knowledge |
| **Performance** | Lightweight | Heavier with extra features |

## Best Practices and Recommendations

### Image Optimization Guidelines
- **Resolution**: 1920x1080 pixels minimum for full-screen coverage
- **File Size**: Keep under 500KB for optimal loading performance
- **Format**:
  - JPG for photographic images (better compression)
  - PNG for graphics with transparency or sharp edges
- **Aspect Ratio**: 16:9 works best for most screen sizes
- **Compression**: Use tools like TinyPNG or ImageOptim before uploading

### Design Considerations
- **Text Readability**: Ensure content remains readable over background images
- **Contrast**: Test text contrast against your background image
- **Cultural Appropriateness**: Choose images that respect PNG cultural heritage
- **Professional Appearance**: Maintain government site standards
- **Mobile Responsiveness**: Test on various screen sizes and devices

### Performance Optimization
- **Image Compression**: Always compress images before uploading
- **CDN Usage**: Consider using a CDN for faster image delivery
- **Lazy Loading**: Background images load immediately, so optimize file size
- **Mobile Considerations**: Large images can slow mobile loading
- **Caching**: Background images are cached by browsers automatically

## Troubleshooting Guide

### Common Issues and Solutions

#### 1. Background Image Not Displaying
**Symptoms**: Image uploaded but not visible on site
**Diagnosis Steps**:
```
1. Check Media Library: Verify image uploaded successfully
2. Check URL: Visit image URL directly in browser
3. Check CSS: Use browser developer tools to inspect body element
4. Check Function: Verify nols_espa_background() is running
```
**Solutions**:
- Clear browser cache and any caching plugins
- Re-upload the image if URL is broken
- Check file permissions on uploads directory
- Verify theme is active and functions.php is loading

#### 2. Image Quality Issues
**Symptoms**: Blurry, pixelated, or stretched images
**Solutions**:
- Upload higher resolution images (minimum 1920x1080)
- Use proper aspect ratios (16:9 recommended)
- Check image compression settings
- Avoid upscaling small images

#### 3. Performance Issues
**Symptoms**: Slow page loading with background images
**Solutions**:
- Compress images before uploading (target <500KB)
- Use JPG format for photos, PNG only when transparency needed
- Consider using CSS gradients for simple color backgrounds
- Test loading times on mobile devices

#### 4. CSS Conflicts
**Symptoms**: Background not applying or being overridden
**Diagnosis**:
```css
/* Check in browser developer tools if this CSS is present: */
body {
    background-image: url(YOUR_IMAGE_URL);
    background-size: cover;
    background-position: center;
}
```
**Solutions**:
- Check for conflicting CSS in other plugins or themes
- Verify the CSS is being output in the `<head>` section
- Increase CSS specificity if needed

### Development and Customization

#### Modifying the Background System
The system can be easily customized by modifying two functions:

**1. Customizer Control (functions.php lines 448-463)**
```php
// Add additional controls like background position, repeat, etc.
$wp_customize->add_setting('bg_position', array(
    'default' => 'center',
));
```

**2. CSS Output (functions.php lines 515-526)**
```php
// Modify CSS properties
echo 'body {
    background-image: url(' . esc_url($bg_image) . ');
    background-size: cover;
    background-position: center;
    background-repeat: no-repeat;
    background-attachment: fixed;
}';
```

#### Adding Background Color Support
To add a background color option alongside the image:
```php
// In customizer function
$wp_customize->add_setting('bg_color');
$wp_customize->add_control(new WP_Customize_Color_Control($wp_customize, 'bg_color', array(
    'label' => 'Background Color',
    'section' => 'bg_image',
)));

// In CSS output function
$bg_color = get_theme_mod('bg_color');
if ($bg_color) {
    echo 'background-color: ' . esc_attr($bg_color) . ';';
}
```

## Cultural and Design Guidelines

### PNG Cultural Considerations
When selecting background images for the Dakoii Provincial Government Theme:
- **Respect Cultural Heritage**: Choose images that honor PNG and East Sepik traditions
- **Color Harmony**: Select images that complement PNG flag colors (red, green, yellow)
- **Traditional Elements**: Consider incorporating traditional patterns or cultural symbols
- **Professional Standards**: Maintain appropriate government site appearance
- **Community Sensitivity**: Avoid images that might be culturally inappropriate

### Design Best Practices
- **Text Legibility**: Ensure all text remains readable over background images
- **Contrast Ratios**: Maintain WCAG accessibility standards for text contrast
- **Visual Hierarchy**: Background should support, not compete with, content
- **Responsive Design**: Test appearance on various screen sizes
- **Loading Experience**: Consider how the page looks while image loads

## Technical Support and Maintenance

### System Requirements
- **WordPress Version**: 5.0 or higher
- **PHP Version**: 7.4 or higher
- **Theme**: Dakoii Provincial Government Theme
- **Browser Support**: All modern browsers (Chrome, Firefox, Safari, Edge)

### Backup and Recovery
- **Settings Backup**: Background settings are stored in WordPress database
- **Image Backup**: Images are stored in `/wp-content/uploads/`
- **Code Backup**: Custom functions are in theme's `functions.php`

### Getting Help
1. **Check This Guide**: Review troubleshooting section first
2. **WordPress Codex**: Reference WordPress customizer documentation
3. **Theme Support**: Contact theme developer with specific error messages
4. **Community Forums**: WordPress.org support forums for general issues

---

## Summary

The Dakoii Provincial Government Theme's background image feature is a **custom-built, lightweight solution** that provides reliable image upload and display functionality without the complexity of WordPress's native background system. With just 16 lines of code, it offers a simple, conflict-free way to customize your site's background while maintaining the beautiful cultural gradient as a fallback.
