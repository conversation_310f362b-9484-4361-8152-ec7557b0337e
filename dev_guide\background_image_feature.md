# Dakoii Provincial Government Theme - Background Image Feature

## Overview
This document describes the comprehensive background image system implemented in the Dakoii Provincial Government Theme. The system provides users with full control over background images through the WordPress Customizer, including advanced styling options and performance optimizations.

## Current Implementation Status
✅ **IMPLEMENTED** - Comprehensive background image system with full customizer integration

## Features
- **Image Upload**: Easy background image upload through WordPress Media Library
- **Background Color**: Optional background color as fallback or overlay
- **Repeat Options**: No-repeat, tile, tile horizontally, tile vertically
- **Position Control**: 9 different positioning options (left/center/right + top/center/bottom)
- **Size Control**: Original size, fit to screen, fill screen
- **Attachment Control**: Fixed (parallax effect) or scroll with page
- **Responsive Design**: Automatic mobile optimizations
- **Performance**: Image preloading and high DPI support
- **Cultural Integration**: Falls back to PNG cultural gradient when no image is set

## How to Use the Background Image Feature

### Accessing the Settings
1. **WordPress Admin**: Go to `Appearance > Customize`
2. **Find Section**: Look for "Background Settings" in the customizer panel
3. **Configure Options**: Use the various controls to customize your background

### Available Controls

#### 1. Background Image
- **Purpose**: Upload your main background image
- **Recommended Size**: 1920x1080px or larger for best quality
- **Supported Formats**: JPG, PNG, WebP
- **File Size**: Keep under 500KB for optimal performance

#### 2. Background Color
- **Purpose**: Optional color that shows behind the image or as fallback
- **Use Cases**:
  - Overlay color for semi-transparent images
  - Fallback when no image is uploaded
  - Solid color backgrounds instead of images

#### 3. Background Repeat
- **No Repeat**: Image appears once (recommended for photos)
- **Tile**: Image repeats in all directions (good for patterns)
- **Tile Horizontally**: Image repeats left-to-right only
- **Tile Vertically**: Image repeats top-to-bottom only

#### 4. Background Position
Choose where your image is positioned:
- **Left**: Top, Center, Bottom
- **Center**: Top, Center, Bottom
- **Right**: Top, Center, Bottom

#### 5. Background Size
- **Original Size**: Image displays at actual dimensions
- **Fit to Screen**: Image scales to fit entirely within viewport
- **Fill Screen**: Image scales to cover entire viewport (recommended)

#### 6. Background Attachment
- **Fixed (Parallax Effect)**: Background stays in place while content scrolls
- **Scroll with Page**: Background moves with page content

## Technical Implementation Details

### Current System Architecture

The background image system is implemented in `functions.php` with the following components:

#### 1. Customizer Section Registration
Located in `nols_espa_theme_customize_register()` function (lines ~447-572):

```php
// Comprehensive Background Settings Section
$wp_customize->add_section('nols_espa_background_section', array(
    'title'       => esc_html__('Background Settings', 'dakoii-provincial-government-theme'),
    'description' => esc_html__('Customize your site background with image and styling options.', 'dakoii-provincial-government-theme'),
    'priority'    => 80,
));
```

#### 2. Settings Registration
All background settings use the `nols_espa_background_` prefix:
- `nols_espa_background_image` - Main background image
- `nols_espa_background_color` - Background color (fallback/overlay)
- `nols_espa_background_repeat` - How image repeats
- `nols_espa_background_position` - Image positioning
- `nols_espa_background_size` - Image sizing
- `nols_espa_background_attachment` - Fixed or scroll behavior

#### 3. CSS Output Function
Located in `nols_espa_output_background_styles()` function (lines ~621-722):

```php
function nols_espa_output_background_styles() {
    $bg_image = get_theme_mod('nols_espa_background_image');
    $bg_color = get_theme_mod('nols_espa_background_color');
    $colors = nols_espa_get_current_colors();

    // Preload background image for better performance
    if ($bg_image) {
        echo '<link rel="preload" as="image" href="' . esc_url($bg_image) . '">';
    }

    // Output comprehensive CSS with responsive design
    // ... (full implementation in functions.php)
}
add_action('wp_head', 'nols_espa_output_background_styles', 5);
```

#### 4. Data Sanitization
Custom sanitization functions ensure data integrity:
- `nols_espa_sanitize_background_repeat()`
- `nols_espa_sanitize_background_position()`
- `nols_espa_sanitize_background_size()`
- `nols_espa_sanitize_background_attachment()`

## System Features & Benefits

### Performance Optimizations
1. **Image Preloading**: Critical background images are preloaded for faster rendering
2. **Responsive Design**: Automatic mobile optimizations (scroll attachment, contain sizing)
3. **High DPI Support**: Optimized rendering for retina displays
4. **Early CSS Injection**: Background styles load with priority 5 for faster rendering

### Cultural Integration
- **Gradient Fallback**: When no image is uploaded, system falls back to PNG cultural gradient
- **Color Harmony**: Background color options integrate with theme's cultural color system
- **Brand Consistency**: Maintains PNG national colors and cultural identity

### User Experience
- **Live Preview**: All changes preview instantly in the customizer
- **Comprehensive Controls**: Full control over all CSS background properties
- **Helpful Descriptions**: Each control includes user-friendly descriptions
- **Organized Interface**: Logical grouping and prioritization of controls

### Data Storage & Management
- **Setting Names**: All settings use `nols_espa_background_` prefix for organization
- **Storage Method**: WordPress theme modifications (`theme_mods`)
- **Database Location**: `wp_options` table under `theme_mods_dakoii-provincial-government-theme`
- **Data Validation**: Custom sanitization functions ensure data integrity

### CSS Output Logic
1. **Image Present**:
   - Outputs all background properties (image, repeat, position, size, attachment)
   - Includes background color if specified
   - Adds responsive optimizations for mobile

2. **No Image**:
   - Falls back to cultural gradient using theme colors
   - Uses background color if specified
   - Maintains cultural branding

3. **Mobile Responsive**:
   - Forces `scroll` attachment on mobile for better performance
   - Uses `contain` sizing to prevent performance issues
   - Maintains visual quality across devices

## Best Practices & Recommendations

### Image Guidelines
1. **Optimal Dimensions**: 1920x1080px or higher for full-screen coverage
2. **File Formats**:
   - JPG for photographs (better compression)
   - PNG for graphics with transparency
   - WebP for modern browsers (future enhancement)
3. **File Size**: Keep under 500KB for optimal loading performance
4. **Aspect Ratio**: 16:9 works best for most screen sizes

### Usage Recommendations
1. **Cover vs Contain**: Use "Fill Screen" (cover) for most backgrounds
2. **Fixed vs Scroll**: "Fixed" creates parallax effect, "Scroll" is better for mobile
3. **Position**: "Center Center" works best for most images
4. **Repeat**: Use "No Repeat" for photos, "Tile" for patterns

### Performance Tips
1. **Image Optimization**: Compress images before uploading
2. **Mobile Considerations**: System automatically optimizes for mobile
3. **Testing**: Test with different screen sizes and devices
4. **Fallbacks**: Always have a background color as fallback

### Cultural Considerations
1. **PNG Colors**: Use colors that complement PNG national colors
2. **Cultural Sensitivity**: Choose images appropriate for government context
3. **Accessibility**: Ensure sufficient contrast with text content
4. **Professional Appearance**: Maintain government website standards

## Testing Your Implementation

1. **Test with different image formats** (JPEG, PNG, WebP)
2. **Check responsive behavior** on various devices
3. **Verify customizer preview** works correctly
4. **Test with no image** to ensure fallbacks work
5. **Check page load performance** with large images

## Troubleshooting Common Issues

### Issue: Background not showing
- Ensure `wp_head()` is called in header.php
- Check if theme support is properly added
- Verify image URL is correct and accessible

### Issue: Background not covering full screen
- Check CSS specificity conflicts
- Ensure body has min-height: 100vh
- Verify background-size property is set correctly

### Issue: Performance problems with large images
- Implement image optimization on upload
- Use CDN for image delivery
- Consider progressive image loading

This implementation provides a robust, user-friendly way to add custom background images to your WordPress theme while maintaining good performance and user experience.