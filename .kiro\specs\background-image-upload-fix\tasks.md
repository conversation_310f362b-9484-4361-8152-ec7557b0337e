# Implementation Plan

- [x] 1. Remove WordPress native background support to prevent conflicts


  - Remove `add_theme_support('custom-background')` from theme setup function
  - Update existing `nols_espa_custom_background_styles()` function to remove references to `get_background_image()` and `get_background_color()`
  - _Requirements: 3.1, 3.4_



- [ ] 2. Implement custom background image customizer controls
  - Create new function `nols_espa_add_background_customizer()` in the existing customizer registration function
  - Add custom "Background Image" section with proper priority and description
  - Register `bg_image_url` setting with `esc_url_raw` sanitization and refresh transport


  - Add `WP_Customize_Image_Control` for image upload functionality
  - _Requirements: 1.1, 1.2, 1.3, 3.2_

- [ ] 3. Create custom background CSS output function
  - Implement new function `nols_espa_output_background_css()` to replace existing background styles
  - Retrieve background image URL using `get_theme_mod('bg_image_url')`
  - Generate CSS for background image with cover, center, no-repeat properties


  - Implement fallback to cultural gradient using existing color system
  - Add mobile optimization with background-attachment: scroll
  - Hook function to `wp_head` action with appropriate priority
  - _Requirements: 2.1, 2.2, 2.3, 4.1, 4.2, 4.3_




- [ ] 4. Update existing background styles function
  - Modify `nols_espa_custom_background_styles()` to work with custom implementation
  - Remove WordPress native background function calls
  - Ensure cultural gradient fallback works properly
  - Maintain existing responsive optimizations
  - _Requirements: 2.2, 2.4, 3.3_

- [ ] 5. Test and verify background image functionality
  - Write test code to verify customizer section appears correctly
  - Create test function to validate image upload and CSS output
  - Add error handling for missing or invalid image URLs
  - Verify fallback gradient displays when no image is set
  - _Requirements: 1.4, 2.1, 2.4, 3.1, 4.4_