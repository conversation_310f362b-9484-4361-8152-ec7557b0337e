<?php
/**
 * Test script to debug background image functionality
 * Place this file in the theme root and access via browser to test
 */

// Load WordPress
require_once('../../../wp-load.php');

// Get current theme mods
$bg_image = get_theme_mod('background_image');
$bg_color = get_theme_mod('background_color');
$bg_repeat = get_theme_mod('background_repeat', 'no-repeat');
$bg_position = get_theme_mod('background_position', 'center center');
$bg_size = get_theme_mod('background_size', 'cover');
$bg_attachment = get_theme_mod('background_attachment', 'fixed');

echo "<h1>Background Image Debug Information</h1>";
echo "<h2>Current Settings:</h2>";
echo "<p><strong>Background Image:</strong> " . ($bg_image ? esc_html($bg_image) : 'Not set') . "</p>";
echo "<p><strong>Background Color:</strong> " . ($bg_color ? esc_html($bg_color) : 'Not set') . "</p>";
echo "<p><strong>Background Repeat:</strong> " . esc_html($bg_repeat) . "</p>";
echo "<p><strong>Background Position:</strong> " . esc_html($bg_position) . "</p>";
echo "<p><strong>Background Size:</strong> " . esc_html($bg_size) . "</p>";
echo "<p><strong>Background Attachment:</strong> " . esc_html($bg_attachment) . "</p>";

echo "<h2>URL Validation:</h2>";
if ($bg_image) {
    $parsed_url = parse_url($bg_image);
    echo "<p><strong>Parsed URL:</strong> " . print_r($parsed_url, true) . "</p>";
    
    $is_valid = filter_var($bg_image, FILTER_VALIDATE_URL);
    echo "<p><strong>Is Valid URL:</strong> " . ($is_valid ? 'Yes' : 'No') . "</p>";
    
    if ($parsed_url && isset($parsed_url['host'])) {
        echo "<p><strong>Host:</strong> " . esc_html($parsed_url['host']) . "</p>";
        echo "<p><strong>Is Invalid IP (*******):</strong> " . ($parsed_url['host'] === '*******' ? 'Yes' : 'No') . "</p>";
    }
} else {
    echo "<p>No background image set.</p>";
}

echo "<h2>Test Image URL:</h2>";
if ($bg_image) {
    echo "<p>Trying to load image:</p>";
    echo "<img src='" . esc_url($bg_image) . "' style='max-width: 200px; max-height: 200px;' onerror='this.style.display=\"none\"; this.nextSibling.style.display=\"block\";'>";
    echo "<p style='display:none; color:red;'>Image failed to load!</p>";
}

echo "<h2>All Theme Mods:</h2>";
$all_mods = get_theme_mods();
echo "<pre>" . print_r($all_mods, true) . "</pre>";
?>
