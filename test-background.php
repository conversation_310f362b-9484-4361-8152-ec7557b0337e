<?php
/**
 * Test script to debug WordPress native background functionality
 * Place this file in the theme root and access via browser to test
 */

// Load WordPress
require_once('../../../wp-load.php');

// Get WordPress native background functions
$wp_bg_image = get_background_image();
$wp_bg_color = get_background_color();

// Get theme mods for additional settings
$bg_repeat = get_theme_mod('background_repeat', 'no-repeat');
$bg_position_x = get_theme_mod('background_position_x', 'center');
$bg_position_y = get_theme_mod('background_position_y', 'center');
$bg_attachment = get_theme_mod('background_attachment', 'scroll');
$bg_size = get_theme_mod('background_size', 'auto');

echo "<h1>WordPress Native Background Debug Information</h1>";
echo "<h2>WordPress Native Functions:</h2>";
echo "<p><strong>get_background_image():</strong> " . ($wp_bg_image ? esc_html($wp_bg_image) : 'Not set') . "</p>";
echo "<p><strong>get_background_color():</strong> " . ($wp_bg_color ? esc_html($wp_bg_color) : 'Not set') . "</p>";

echo "<h2>Additional Theme Mods:</h2>";
echo "<p><strong>Background Repeat:</strong> " . esc_html($bg_repeat) . "</p>";
echo "<p><strong>Background Position X:</strong> " . esc_html($bg_position_x) . "</p>";
echo "<p><strong>Background Position Y:</strong> " . esc_html($bg_position_y) . "</p>";
echo "<p><strong>Background Size:</strong> " . esc_html($bg_size) . "</p>";
echo "<p><strong>Background Attachment:</strong> " . esc_html($bg_attachment) . "</p>";

echo "<h2>Theme Support Check:</h2>";
$bg_support = get_theme_support('custom-background');
echo "<p><strong>Custom Background Support:</strong> " . ($bg_support ? 'Enabled' : 'Disabled') . "</p>";
if ($bg_support) {
    echo "<p><strong>Support Details:</strong> " . print_r($bg_support, true) . "</p>";
}

echo "<h2>URL Validation:</h2>";
if ($wp_bg_image) {
    $parsed_url = parse_url($wp_bg_image);
    echo "<p><strong>Parsed URL:</strong> " . print_r($parsed_url, true) . "</p>";

    $is_valid = filter_var($wp_bg_image, FILTER_VALIDATE_URL);
    echo "<p><strong>Is Valid URL:</strong> " . ($is_valid ? 'Yes' : 'No') . "</p>";

    if ($parsed_url && isset($parsed_url['host'])) {
        echo "<p><strong>Host:</strong> " . esc_html($parsed_url['host']) . "</p>";
        echo "<p><strong>Is Invalid IP (*******):</strong> " . ($parsed_url['host'] === '*******' ? 'Yes' : 'No') . "</p>";
    }
} else {
    echo "<p>No background image set.</p>";
}

echo "<h2>Test Image URL:</h2>";
if ($wp_bg_image) {
    echo "<p>Trying to load image:</p>";
    echo "<img src='" . esc_url($wp_bg_image) . "' style='max-width: 200px; max-height: 200px;' onerror='this.style.display=\"none\"; this.nextSibling.style.display=\"block\";'>";
    echo "<p style='display:none; color:red;'>Image failed to load!</p>";
}

echo "<h2>All Theme Mods:</h2>";
$all_mods = get_theme_mods();
echo "<pre>" . print_r($all_mods, true) . "</pre>";

echo "<h2>Background CSS Preview:</h2>";
echo "<p>This is what the CSS would look like:</p>";
echo "<pre>";
if ($wp_bg_image) {
    echo "body.custom-background {\n";
    echo "    background-image: url('" . esc_url($wp_bg_image) . "');\n";
    echo "    background-repeat: " . esc_attr($bg_repeat) . ";\n";
    echo "    background-position: " . esc_attr($bg_position_x) . " " . esc_attr($bg_position_y) . ";\n";
    echo "    background-attachment: " . esc_attr($bg_attachment) . ";\n";
    if ($bg_size !== 'auto') {
        echo "    background-size: " . esc_attr($bg_size) . ";\n";
    }
    if ($wp_bg_color) {
        echo "    background-color: #" . esc_attr($wp_bg_color) . ";\n";
    }
    echo "}\n";
} else {
    echo "/* No background image set - would use cultural gradient fallback */\n";
}
echo "</pre>";
?>
